import { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { getOrder } from '../../redux/slices/orderSlice';
import StripePaymentForm from '../../components/payment/StripePaymentForm';
import LoadingSkeleton from '../../components/common/LoadingSkeleton';
import { ErrorDisplay } from '../../components/common/ErrorBoundary';
import { toast } from 'react-toastify';
import { VALIDATION } from '../../utils/constants';
import '../../styles/CheckoutPage.css';

const CheckoutPage = () => {
  const { orderId } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const { user } = useSelector((state) => state.auth);
  const { isLoading, error } = useSelector((state) => state.order);

  const [paymentStep, setPaymentStep] = useState('loading'); // loading, payment, success, error
  const [currentOrder, setCurrentOrder] = useState(null); // Local state for current order



  useEffect(() => {
    console.log('🔍 CheckoutPage useEffect - orderId from URL:', orderId);
    console.log('🔍 Current window.location.pathname:', window.location.pathname);

    // Check if user is logged in
    if (!user) {
      toast.error('Please log in to complete your purchase');
      navigate('/login');
      return;
    }

    // Check if user is a buyer
    if (user.role !== 'buyer') {
      toast.error('Only buyers can make purchases');
      navigate('/');
      return;
    }

    // Validate order ID using utility function
    if (!VALIDATION.isValidId(orderId)) {
      console.error('Invalid order ID:', orderId);
      toast.error('Invalid order ID. Please try creating a new order.');
      navigate('/buyer/dashboard');
      return;
    }

    // Fetch order details
    dispatch(getOrder(orderId))
      .unwrap()
      .then((result) => {
        console.log('Order fetched successfully, setting payment step to payment');
        console.log('Order data:', result);
        console.log('✅ Fetched order ID:', result.data._id);
        console.log('✅ URL order ID:', orderId);
        console.log('✅ IDs match:', result.data._id === orderId);

        // Set the order in local state
        setCurrentOrder(result.data);
        setPaymentStep('payment');
        console.log('✅ Order set in local state:', result.data._id);
      })
      .catch((err) => {
        console.error('Error fetching order:', err);
        toast.error('Order not found or you do not have permission to view it');
        navigate('/buyer/dashboard');
      });
  }, [dispatch, orderId, user, navigate]);

  const handlePaymentSuccess = (paymentResult) => {
    toast.success('Payment completed successfully!');
    setPaymentStep('success');

    // Prepare order data for ThankYou page
    const orderData = {
      orderId: `#${currentOrder._id?.slice(-8) || "12345678"}`,
      date: new Date(currentOrder.createdAt || Date.now()).toLocaleDateString("en-US", {
        day: "numeric",
        month: "short",
        year: "numeric",
      }),
      time: new Date(currentOrder.createdAt || Date.now()).toLocaleTimeString("en-US", {
        hour: "numeric",
        minute: "2-digit",
        hour12: true,
      }),
      items: 1,
      totalAmount: `$${currentOrder.amount || "0.00"}`,
      customerDetails: {
        name: currentOrder.buyer?.firstName && currentOrder.buyer?.lastName
          ? `${currentOrder.buyer.firstName} ${currentOrder.buyer.lastName}`
          : currentOrder.buyer?.name || "Customer",
        email: currentOrder.buyer?.email || "<EMAIL>",
        phone: currentOrder.buyer?.mobile || currentOrder.buyer?.phone || "************",
      },
      paymentDetails: {
        method: "Card Payment",
        cardNumber: "**** **** **** ****",
      },
      itemInfo: {
        title: currentOrder.content?.title || "Digital Content",
        category: currentOrder.content?.category || currentOrder.content?.sport || "Sports Content",
        image: currentOrder.content?.thumbnail || currentOrder.content?.thumbnailUrl ||
               "https://via.placeholder.com/80x80/f0f0f0/666666?text=Content",
      },
      // Include full order and payment data for download functionality
      fullOrder: currentOrder,
      paymentResult: paymentResult
    };

    // Navigate to thank you page after a short delay
    setTimeout(() => {
      navigate('/thank-you', {
        state: { orderData }
      });
    }, 2000);
  };

  const handlePaymentError = (error) => {
    console.error('Payment error:', error);
    toast.error(error.message || 'Payment failed. Please try again.');
    setPaymentStep('error');
  };

  const handlePaymentCancel = () => {
    navigate(`/buyer/details/${currentOrder?.content?._id || currentOrder?.content}`);
  };

  const handleRetryPayment = () => {
    setPaymentStep('payment');
  };

  console.log('🔍 Early return checks:');
  console.log('🔍 isLoading:', isLoading);
  console.log('🔍 paymentStep:', paymentStep);
  console.log('🔍 paymentStep === loading:', paymentStep === 'loading');
  console.log('🔍 error:', error);
  console.log('🔍 currentOrder:', currentOrder);
  console.log('🔍 !currentOrder:', !currentOrder);

  if (isLoading || paymentStep === 'loading') {
    console.log('🔍 Returning LoadingSkeleton');
    return <LoadingSkeleton type="checkout" />;
  }

  if (error || !currentOrder) {
    console.log('🔍 Returning ErrorDisplay - error:', error, 'currentOrder:', !!currentOrder);
    return (
      <ErrorDisplay
        title="Order Not Found"
        message={error || "The order you're looking for doesn't exist or you don't have permission to view it."}
        onRetry={() => navigate('/buyer/dashboard')}
        retryText="Go to Dashboard"
      />
    );
  }

  // Check if order belongs to current user
  console.log('🔍 User permission check:');
  console.log('🔍 currentOrder.buyer._id:', currentOrder.buyer._id);
  console.log('🔍 user._id:', user._id);
  console.log('🔍 Fixed permission check:', currentOrder.buyer._id !== user._id);

  if (currentOrder.buyer._id !== user._id && currentOrder.buyer !== user._id) {
    console.log('🔍 Returning Access Denied');
    return (
      <ErrorDisplay
        title="Access Denied"
        message="You don't have permission to view this order."
        onRetry={() => navigate('/buyer/dashboard')}
        retryText="Go to Dashboard"
      />
    );
  }

  // Check if order is already paid
  console.log('🔍 Payment status check:', currentOrder.paymentStatus);
  if (currentOrder.paymentStatus === 'Completed') {
    return (
      <div className="checkout-page">
        <div className="max-container">
          <div className="checkout-content">
            <div className="order-already-paid">
              <h2>Order Already Paid</h2>
              <p>This order has already been completed.</p>
              <button 
                className="btn-primary"
                onClick={() => navigate('/buyer/downloads')}
              >
                View Downloads
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  console.log('🎯 CheckoutPage render - paymentStep:', paymentStep, 'order exists:', !!currentOrder);
  console.log('🎯 CheckoutPage render - order ID:', currentOrder?._id);
  console.log('🎯 CheckoutPage render - expected order ID:', orderId);
  console.log('🎯 CheckoutPage render - order ID matches:', currentOrder?._id === orderId);
  console.log('🎯 CheckoutPage render - condition check:', paymentStep === 'payment' && currentOrder);
  console.log('🎯 CheckoutPage render - paymentStep === payment:', paymentStep === 'payment');
  console.log('🎯 CheckoutPage render - currentOrder truthy:', !!currentOrder);

  return (
    <div className="checkout-page">
      <div className="max-container">
        <div className="checkout-content">
          {/* Left Section - Payment Form */}
          <div className="checkout-left">
            <div className="checkout-form-container">
              <h1 className="checkout-title">Complete Your Purchase</h1>

              {paymentStep === 'loading' && (
                <div className="payment-loading">
                  <p>Loading order details...</p>
                </div>
              )}

              {paymentStep === 'payment' && currentOrder ? (
                <>
                  {console.log('✅ Rendering StripePaymentForm for order:', currentOrder._id)}
                  <StripePaymentForm
                    order={currentOrder}
                    onSuccess={handlePaymentSuccess}
                    onError={handlePaymentError}
                    onCancel={handlePaymentCancel}
                  />
                </>
              ) : (
                <div className="debug-info">
                  <h3>Debug Information</h3>
                  <p>Payment Step: {paymentStep}</p>
                  <p>Order exists: {currentOrder ? 'Yes' : 'No'}</p>
                  <p>Order ID: {currentOrder?._id || 'N/A'}</p>
                  {!currentOrder && <p>❌ Order is missing</p>}
                  {paymentStep !== 'payment' && <p>❌ Payment step is not 'payment'</p>}
                </div>
              )}

              {paymentStep === 'success' && (
                <div className="payment-success">
                  <div className="success-icon">✅</div>
                  <h3>Payment Successful!</h3>
                  <p>Your payment has been processed successfully. Redirecting...</p>
                </div>
              )}

              {paymentStep === 'error' && (
                <div className="payment-error">
                  <div className="error-icon">❌</div>
                  <h3>Payment Failed</h3>
                  <p>There was an issue processing your payment. Please try again.</p>
                  <button 
                    className="btn-primary retry-btn"
                    onClick={handleRetryPayment}
                  >
                    Retry Payment
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Right Section - Order Summary */}
          <div className="checkout-right">
            <div className="order-summary">
              <h2 className="order-title">Order Summary</h2>

              <div className="rightbackgrounddiv">
                {/* Item Info */}
                <div className="item-info-section">
                  <h3 className="item-info-title">Item Details</h3>

                  <div className="item-details">
                    <div className="item-image">
                      <img
                        src={currentOrder.content?.thumbnailUrl || "https://via.placeholder.com/80x80/f5f5f5/666666?text=IMG"}
                        alt={currentOrder.content?.title || "Content"}
                        className="item-thumbnail"
                      />
                    </div>

                    <div className="item-description">
                      <h4 className="item-name">
                        {currentOrder.content?.title || "Content Title"}
                      </h4>
                      <p className="item-coach">
                        By {currentOrder.content?.coachName || "Coach"}
                      </p>
                      <p className="item-type">
                        {currentOrder.content?.contentType || "Digital Content"}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Order Info */}
                <div className="order-info-section">
                  <h3 className="order-info-title">Order Information</h3>
                  <div className="order-details">
                    <div className="order-row">
                      <span>Order ID:</span>
                      <span>#{currentOrder._id?.slice(-8).toUpperCase()}</span>
                    </div>
                    <div className="order-row">
                      <span>Order Type:</span>
                      <span>{currentOrder.orderType}</span>
                    </div>
                    <div className="order-row">
                      <span>Status:</span>
                      <span className={`status ${currentOrder.status?.toLowerCase()}`}>
                        {currentOrder.status}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Pricing */}
                <div className="pricing-section">
                  <div className="price-row">
                    <span className="price-label">Subtotal</span>
                    <span className="price-value">${currentOrder.amount?.toFixed(2)}</span>
                  </div>

                  {currentOrder.platformFee > 0 && (
                    <div className="price-row">
                      <span className="price-label">Platform Fee</span>
                      <span className="price-value">${currentOrder.platformFee?.toFixed(2)}</span>
                    </div>
                  )}

                  <div className="price-row total-row">
                    <span className="price-label">Total</span>
                    <span className="price-value">${currentOrder.amount?.toFixed(2)}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CheckoutPage;
